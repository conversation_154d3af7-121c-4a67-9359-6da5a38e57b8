package by.algin.webuiservice.exception;

import by.algin.common.BaseExceptionHandler;
import by.algin.common.exception.CommonErrorCodes;
import by.algin.dto.response.ApiResponse;
import by.algin.webuiservice.constants.MessageConstants;
import by.algin.exception.FeignErrorHandler;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@Slf4j
@RestControllerAdvice
public class WebUIExceptionHandler extends BaseExceptionHandler {

    private final FeignErrorHandler feignErrorHandler;

    public WebUIExceptionHandler(FeignErrorHandler feignErrorHandler) {
        this.feignErrorHandler = feignErrorHandler;
    }

    @Override
    protected String getServiceName() {
        return "WebUIService";
    }

    @ExceptionHandler(FeignException.class)
    public ResponseEntity<ApiResponse<Object>> handleFeignException(FeignException ex) {
        log.warn("Feign client error: HTTP {} - {}", ex.status(), ex.getMessage());

        String userFriendlyMessage = feignErrorHandler.extractUserFriendlyMessage(ex);

        switch (ex.status()) {
            case 400:
                return createMappedError(ex, "VALIDATION_ERROR", userFriendlyMessage, 400);
            case 401:
                return createMappedError(ex, "AUTHENTICATION_REQUIRED",
                                       MessageConstants.AUTHENTICATION_REQUIRED, 401);
            case 403:
                return createMappedError(ex, "ACCESS_DENIED",
                                       MessageConstants.PERMISSION_DENIED_CREATE_PROJECTS, 403);
            case 404:
                return createMappedError(ex, "RESOURCE_NOT_FOUND",
                                       MessageConstants.PROJECT_NOT_FOUND, 404);
            case 409:
                return createMappedError(ex, "RESOURCE_CONFLICT",
                                       MessageConstants.PROJECT_NAME_EXISTS, 409);
            case 503:
                return createMappedError(ex, "SERVICE_UNAVAILABLE",
                                       MessageConstants.SERVICE_TEMPORARILY_UNAVAILABLE, 503);
            default:
                return createMappedError(ex, "EXTERNAL_SERVICE_ERROR",
                                       MessageConstants.PROJECT_SERVICE_UNAVAILABLE, 502);
        }
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<Object>> handleIllegalArgument(IllegalArgumentException ex) {
        log.warn("UI validation error: {}", ex.getMessage());
        return createMappedError(ex, "UI_VALIDATION_ERROR", ex.getMessage(), 400);
    }


}
