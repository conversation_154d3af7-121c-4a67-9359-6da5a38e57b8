package by.algin.webuiservice.service;

import by.algin.constants.CommonPathConstants;
import by.algin.dto.request.RegisterRequest;
import by.algin.dto.request.TokenValidationRequest;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.AuthResponse;
import by.algin.dto.response.UserResponse;
import by.algin.webuiservice.client.AuthServiceClient;
import by.algin.constants.CommonRoleConstants;
import by.algin.constants.CommonTemplateConstants;
import by.algin.webuiservice.constants.MessageConstants;
import by.algin.webuiservice.constants.ModelAttributeConstants;
import by.algin.webuiservice.util.ModelUtils;
import by.algin.exception.FeignErrorHandler;

import feign.FeignException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;


import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class WebAuthService {

    private final AuthServiceClient authServiceClient;
    private final CookieService cookieService;
    private final FeignErrorHandler feignErrorHandler;

    public void processSuccessfulLogin(AuthResponse authResponse, HttpServletRequest request, HttpServletResponse response) {
        validateAuthResponse(authResponse);
        validateHttpObjects(request, response);

        cookieService.createJwtCookies(authResponse, response);
        setSecurityContext(authResponse, request);

        log.info("Successful login processed for user: {} with roles: {}", authResponse.getUsername(), authResponse.getRoles());
    }

    public void clearAuthenticationCookies(HttpServletResponse response) {
        if (response == null) {
            log.warn("Cannot clear cookies: HttpServletResponse is null");
            return;
        }

        cookieService.clearAuthenticationCookies(response);
        SecurityContextHolder.clearContext();
        log.info(MessageConstants.AUTHENTICATION_CLEARED);
    }

    public String getRegistrationErrorMessage(Exception e) {
        if (e instanceof FeignException feignException) {
            log.error("Registration failed with status {}: {}", feignException.status(), feignException.getMessage());

            if (feignException.status() == 500) {
                return MessageConstants.REGISTRATION_EMAIL_ISSUE;
            }

            return feignErrorHandler.extractAuthUserFriendlyMessage(feignException);
        }

        log.error("Unexpected registration error: {}", e.getMessage(), e);
        return "An unexpected error occurred: " + e.getMessage();
    }

    public boolean hasAdminRole(Set<String> roles) {
        return roles != null && roles.contains(CommonRoleConstants.ADMIN);
    }



    private void setSecurityContext(AuthResponse authResponse, HttpServletRequest request) {
        Set<String> roles = getRolesOrDefault(authResponse);
        List<SimpleGrantedAuthority> authorities = createAuthorities(roles);

        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
            authResponse.getUsername(),
            null,
            authorities
        );
        authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

        SecurityContextHolder.getContext().setAuthentication(authentication);
        
        HttpSession session = request.getSession(true);
        session.setAttribute(HttpSessionSecurityContextRepository.SPRING_SECURITY_CONTEXT_KEY, SecurityContextHolder.getContext());
        
        log.debug("Security context set for user: {} with authorities: {}", authResponse.getUsername(), authorities);
    }

    private Set<String> getRolesOrDefault(AuthResponse authResponse) {
        Set<String> roles = authResponse.getRoles();
        if (roles == null || roles.isEmpty()) {
            log.warn("No roles found for user: {}, assigning default USER role", authResponse.getUsername());
            return Set.of(CommonRoleConstants.USER);
        }
        return roles;
    }

    private List<SimpleGrantedAuthority> createAuthorities(Set<String> roles) {
        return roles.stream()
                .map(SimpleGrantedAuthority::new)
                .collect(Collectors.toList());
    }

    public String processRegistration(RegisterRequest user, BindingResult result, Model model, RedirectAttributes redirectAttributes) {
        if (result.hasErrors()) {
            return CommonTemplateConstants.TEMPLATE_REGISTER;
        }

        if (!validateRegistrationRequest(user, model)) {
            return CommonTemplateConstants.TEMPLATE_REGISTER;
        }

        try {
            log.info("Processing registration for user: {}", user.getUsername());
            ApiResponse<UserResponse> response = authServiceClient.registerUser(user);

            if (!response.isSuccess()) {
                log.warn("Registration failed for user {}: {}", user.getUsername(), response.getMessage());
                ModelUtils.addError(model, response.getMessage());
                ModelUtils.addUser(model, user);
                return CommonTemplateConstants.TEMPLATE_REGISTER;
            }

            log.info("Registration successful for user: {}", user.getUsername());
            return "redirect:" + CommonPathConstants.AUTH_REGISTRATION_SUCCESS;
        } catch (Exception e) {
            return handleRegistrationError(e, model, user);
        }
    }

    public String processAccountConfirmation(String token, RedirectAttributes redirectAttributes) {
        if (token == null || token.trim().isEmpty()) {
            addErrorToRedirect(redirectAttributes, "Invalid confirmation token");
            return "redirect:" + CommonPathConstants.AUTH_TOKEN_EXPIRED;
        }

        try {
            ApiResponse<String> response = authServiceClient.confirmAccount(token);
            if (response == null || !response.isSuccess()) {
                return handleConfirmationFailure(token, redirectAttributes, response);
            }

            addSuccessToRedirect(redirectAttributes, MessageConstants.ACCOUNT_CONFIRMED);
            return "redirect:" + CommonPathConstants.AUTH_ACCOUNT_CONFIRMED;
        } catch (Exception e) {
            return handleConfirmationException(token, redirectAttributes, e);
        }
    }

    private String handleRegistrationError(Exception e, Model model, RegisterRequest user) {
        if (e instanceof FeignException) {
            feignErrorHandler.handleAuthFeignError((FeignException) e, model, "user registration", user.getUsername());
        } else {
            log.error("Unexpected registration error for user {}: {}", user.getUsername(), e.getMessage(), e);
            ModelUtils.addError(model, "An unexpected error occurred: " + e.getMessage());
        }

        ModelUtils.addUser(model, user);
        return CommonTemplateConstants.TEMPLATE_REGISTER;
    }

    private void validateAuthResponse(AuthResponse authResponse) {
        if (authResponse == null || authResponse.getUsername() == null) {
            log.error("Invalid AuthResponse: username is null");
            throw new IllegalArgumentException(MessageConstants.INVALID_AUTH_RESPONSE);
        }
    }

    private void validateHttpObjects(HttpServletRequest request, HttpServletResponse response) {
        if (request == null) {
            throw new IllegalArgumentException("HttpServletRequest cannot be null");
        }
        if (response == null) {
            throw new IllegalArgumentException("HttpServletResponse cannot be null");
        }
    }
    private boolean validateRegistrationRequest(RegisterRequest user, Model model) {
        if (user == null) {
            ModelUtils.addError(model, "Registration data is required");
            return false;
        }

        if (user.getUsername() == null || user.getUsername().trim().isEmpty()) {
            ModelUtils.addError(model, "Username is required");
            ModelUtils.addUser(model, user);
            return false;
        }

        if (user.getEmail() == null || user.getEmail().trim().isEmpty()) {
            ModelUtils.addError(model, "Email is required");
            ModelUtils.addUser(model, user);
            return false;
        }

        return true;
    }

    private void addErrorToRedirect(RedirectAttributes redirectAttributes, String message) {
        ModelUtils.addErrorToRedirect(redirectAttributes, message);
    }

    private void addSuccessToRedirect(RedirectAttributes redirectAttributes, String message) {
        ModelUtils.addSuccessToRedirect(redirectAttributes, message);
    }

    private void tryAddEmailToRedirect(String token, RedirectAttributes redirectAttributes) {
        try {
            ApiResponse<String> emailResponse = authServiceClient.getEmailByToken(token);
            if (emailResponse != null && emailResponse.isSuccess() && emailResponse.getData() != null) {
                String email = emailResponse.getData();
                log.info("Found email for expired token: {}", email);
                redirectAttributes.addAttribute(ModelAttributeConstants.EMAIL_ATTRIBUTE, email);
            }
        } catch (Exception emailEx) {
            log.warn("Could not get email for token: {}", emailEx.getMessage());
        }
    }

    private String handleConfirmationFailure(String token, RedirectAttributes redirectAttributes, ApiResponse<String> response) {
        log.warn("Account confirmation failed for token: {}, response: {}", token, response);

        tryAddEmailToRedirect(token, redirectAttributes);
        redirectAttributes.addAttribute(ModelAttributeConstants.TOKEN_ATTRIBUTE, token);

        String errorMessage = response != null ? response.getMessage() : MessageConstants.ACCOUNT_CONFIRMATION_FAILED;
        addErrorToRedirect(redirectAttributes, errorMessage);

        return "redirect:" + CommonPathConstants.AUTH_TOKEN_EXPIRED;
    }

    private String handleConfirmationException(String token, RedirectAttributes redirectAttributes, Exception e) {
        if (e instanceof FeignException) {
            String userMessage = feignErrorHandler.extractAuthUserFriendlyMessage((FeignException) e);
            addErrorToRedirect(redirectAttributes, userMessage);
            feignErrorHandler.handleAuthFeignError((FeignException) e, null, "account confirmation", "token:" + token);
        } else {
            log.error("Account confirmation failed for token: {}", token, e);
            addErrorToRedirect(redirectAttributes, MessageConstants.ACCOUNT_CONFIRMATION_TOKEN_INVALID);
        }

        tryAddEmailToRedirect(token, redirectAttributes);
        redirectAttributes.addAttribute(ModelAttributeConstants.TOKEN_ATTRIBUTE, token);

        return "redirect:" + CommonPathConstants.AUTH_TOKEN_EXPIRED;
    }
}