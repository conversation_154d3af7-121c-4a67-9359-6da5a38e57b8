package by.algin.projectservice.controller;

import by.algin.api.ProjectRoleApi;
import by.algin.dto.project.ProjectRoleResponse;
import by.algin.dto.response.ApiResponse;
import by.algin.projectservice.entity.ProjectRole;
import by.algin.projectservice.service.ProjectRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Implementation of ProjectRoleApi for ProjectService
 * Provides role information from configuration and database
 */
@RestController
@RequiredArgsConstructor
@Slf4j
public class ProjectRoleController implements ProjectRoleApi {

    private final ProjectRoleService roleService;

    @Override
    public ApiResponse<List<ProjectRoleResponse>> getAvailableRoles() {
        log.debug("Getting all available project roles");

        List<ProjectRole> roles = roleService.getAllActiveRoles();
        List<ProjectRoleResponse> roleResponses = roles.stream()
                .map(this::convertToRoleResponse)
                .collect(Collectors.toList());

        return ApiResponse.success(roleResponses);
    }

    @Override
    public ApiResponse<List<ProjectRoleResponse>> getAssignableRoles() {
        log.debug("Getting assignable project roles");

        List<ProjectRole> roles = roleService.getAssignableRoles();
        List<ProjectRoleResponse> roleResponses = roles.stream()
                .map(this::convertToRoleResponse)
                .collect(Collectors.toList());

        return ApiResponse.success(roleResponses);
    }
    
    private ProjectRoleResponse convertToRoleResponse(ProjectRole role) {
        return ProjectRoleResponse.builder()
                .roleName(role.getRoleName())
                .displayName(role.getDisplayName())
                .description(role.getDescription())
                .permissionLevel(role.getPermissionLevel())
                .colorCode(role.getColorCode())
                .iconName(role.getIconName())
                .isActive(role.getIsActive())
                .canBeAssigned(role.getCanBeAssigned())
                .maxPerProject(role.getMaxPerProject())
                .isSystemRole(role.getIsSystemRole())
                .build();
    }
}
