package by.algin.webuiservice.service;

import by.algin.dto.project.*;
import by.algin.dto.response.ApiResponse;
import by.algin.dto.response.PagedResponse;
import by.algin.dto.response.UserResponse;
import by.algin.webuiservice.client.ProjectServiceClient;
import by.algin.webuiservice.client.UserServiceClient;
import by.algin.webuiservice.constants.ModelAttributeConstants;
import by.algin.webuiservice.constants.MessageConstants;
import by.algin.webuiservice.dto.ServiceResult;
import by.algin.webuiservice.util.ModelUtils;
import by.algin.exception.FeignErrorHandler;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectWebService {

    private static final String MESSAGE_JSON_KEY = "\"message\":\"";
    private static final int MESSAGE_JSON_KEY_LENGTH = 11;
    private static final int MESSAGE_START_OFFSET = 10;

    private static final long MIN_VALID_ID = 0;
    private static final int MAX_USER_FRIENDLY_MESSAGE_LENGTH = 200;

    private static final int LOG_INDEX_OFFSET = 1;

    private final ProjectServiceClient projectServiceClient;
    private final UserServiceClient userServiceClient;
    private final FeignErrorHandler feignErrorHandler;

    public ServiceResult<List<ProjectResponse>> getUserProjects(UserResponse user) {
        try {
            log.debug("Loading projects for user: {}", user.getUsername());
            List<ProjectResponse> projects = projectServiceClient.getUserProjects();

            if (projects != null && !projects.isEmpty()) {
                logProjectDetails(projects);
                return ServiceResult.success(projects);
            } else {
                return ServiceResult.success(Collections.emptyList());
            }
        } catch (Exception e) {
            log.error("Failed to load projects for user: {}, error: {}", user.getUsername(), e.getMessage(), e);
            return ServiceResult.error("Failed to load projects: " + e.getMessage());
        }
    }

    public ServiceResult<ProjectViewData> getProjectViewData(Long projectId, UserResponse user) {
        try {
            ProjectResponse project = projectServiceClient.getProject(projectId);
            log.debug("Loaded project details: {} for user: {}", project.getName(), user.getUsername());

            ProjectMemberListResponse members = projectServiceClient.getProjectMembers(projectId);
            log.debug("Loaded {} members for project: {}", members.getMembers().size(), project.getName());

            ProjectViewData data = new ProjectViewData(project, members, user);
            return ServiceResult.success(data);
        } catch (Exception e) {
            log.error("Project not found: {} for user: {}, error: {}", projectId, user.getUsername(), e.getMessage());
            return ServiceResult.error("Project not found");
        }
    }

    public static class ProjectViewData {
        private final ProjectResponse project;
        private final ProjectMemberListResponse members;
        private final UserResponse user;

        public ProjectViewData(ProjectResponse project, ProjectMemberListResponse members, UserResponse user) {
            this.project = project;
            this.members = members;
            this.user = user;
        }

        public ProjectResponse getProject() { return project; }
        public ProjectMemberListResponse getMembers() { return members; }
        public UserResponse getUser() { return user; }
    }

    public boolean createProject(CreateProjectRequest request, Authentication authentication, Model model) {
        log.debug("Authentication: {}, Is authenticated: {}",
                authentication != null ? authentication.getName() : "null",
                authentication != null ? authentication.isAuthenticated() : "false");

        if (!validateCreateProjectRequest(request, model)) {
            return false;
        }

        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isEmpty()) {
            log.error("User not found for authentication: {}", authentication != null ? authentication.getName() : "null");
            addErrorMessage(model, MessageConstants.USER_NOT_FOUND);
            return false;
        }

        UserResponse user = userOpt.get();
        log.debug("User found: id={}, username={}", user.getId(), user.getUsername());

        return executeProjectCreation(request, user, model);
    }

    public void prepareProjectView(Long projectId, Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = validateProjectAndUser(projectId, authentication, model);
        if (userOpt.isEmpty()) {
            return;
        }

        UserResponse user = userOpt.get();
        loadProjectDetailsAndMembers(projectId, user, model);
    }

    private Optional<UserResponse> validateProjectAndUser(Long projectId, Authentication authentication, Model model) {
        if (!validateProjectId(projectId, model)) {
            return Optional.empty();
        }

        return extractCurrentUserAndAddToModel(authentication, model);
    }

    private Optional<UserResponse> validateProjectAndUserOnly(Long projectId, Authentication authentication, Model model) {
        if (!validateProjectId(projectId, model)) {
            return Optional.empty();
        }

        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isEmpty()) {
            addErrorMessage(model, MessageConstants.USER_NOT_FOUND);
        }
        return userOpt;
    }

    private Optional<UserResponse> validateProjectUserAndMemberId(Long projectId, Long userId, Authentication authentication, Model model) {
        if (!validateProjectId(projectId, model) || !validateUserId(userId, model)) {
            return Optional.empty();
        }

        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isEmpty()) {
            addErrorMessage(model, MessageConstants.USER_NOT_FOUND);
        }
        return userOpt;
    }

    private void loadProjectDetailsAndMembers(Long projectId, UserResponse user, Model model) {
        try {
            ProjectResponse project = loadProjectDetails(projectId, user, model);
            loadProjectMembers(projectId, project, model);
        } catch (Exception e) {
            addErrorMessage(model, MessageConstants.PROJECT_NOT_FOUND);
            log.error("Project not found: {} for user: {}, error: {}", projectId, user.getUsername(), e.getMessage());
        }
    }

    private ProjectResponse loadProjectDetails(Long projectId, UserResponse user, Model model) {
        ProjectResponse project = projectServiceClient.getProject(projectId);
        ModelUtils.addProject(model, project);
        log.info("Loaded project details: {} for user: {}", project.getName(), user.getUsername());
        return project;
    }

    private void loadProjectMembers(Long projectId, ProjectResponse project, Model model) {
        ProjectMemberListResponse members = projectServiceClient.getProjectMembers(projectId);
        ModelUtils.addMembers(model, members.getMembers());
        log.info("Loaded {} members for project: {}", members.getMembers().size(), project.getName());
    }

    public boolean updateProject(Long projectId, UpdateProjectRequest request, Authentication authentication, Model model) {
        if (request == null) {
            addErrorMessage(model, MessageConstants.INVALID_PROJECT_DATA);
            return false;
        }

        Optional<UserResponse> userOpt = validateProjectAndUserOnly(projectId, authentication, model);
        if (userOpt.isEmpty()) {
            return false;
        }

        UserResponse user = userOpt.get();

        try {
            ProjectResponse project = projectServiceClient.updateProject(projectId, request);
            addSuccessMessage(model, String.format(MessageConstants.PROJECT_UPDATED, project.getName()));
            log.info("Project updated successfully: {} by user: {}", project.getName(), user.getUsername());
            return true;
        } catch (Exception e) {
            addErrorMessage(model, MessageConstants.FAILED_TO_UPDATE_PROJECT);
            log.error("Failed to update project: {} for user: {}, error: {}", projectId, user.getUsername(), e.getMessage());
            return false;
        }
    }

    public boolean deleteProject(Long projectId, Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = validateProjectAndUserOnly(projectId, authentication, model);
        if (userOpt.isEmpty()) {
            return false;
        }

        UserResponse user = userOpt.get();

        try {
            projectServiceClient.deleteProject(projectId);
            addSuccessMessage(model, MessageConstants.PROJECT_DELETED);
            log.info("Project deleted successfully: {} by user: {}", projectId, user.getUsername());
            return true;
        } catch (Exception e) {
            addErrorMessage(model, MessageConstants.FAILED_TO_DELETE_PROJECT);
            log.error("Failed to delete project: {} for user: {}, error: {}", projectId, user.getUsername(), e.getMessage());
            return false;
        }
    }

    public boolean addProjectMember(Long projectId, AddProjectMemberRequest request, Authentication authentication, Model model) {
        if (request == null) {
            addErrorMessage(model, "Member request is required");
            return false;
        }

        Optional<UserResponse> userOpt = validateProjectAndUserOnly(projectId, authentication, model);
        if (userOpt.isEmpty()) {
            return false;
        }

        UserResponse user = userOpt.get();

        try {
            ProjectMemberResponse member = projectServiceClient.addProjectMember(projectId, request);
            if (member != null) {
                addSuccessMessage(model, MessageConstants.MEMBER_ADDED);
                log.info("Member added to project: {} by user: {}", projectId, user.getUsername());
                return true;
            } else {
                addErrorMessage(model, MessageConstants.FAILED_TO_ADD_MEMBER);
                log.error("Failed to add member to project: {} for user: {}", projectId, user.getUsername());
                return false;
            }
        } catch (Exception e) {
            addErrorMessage(model, MessageConstants.FAILED_TO_ADD_MEMBER);
            log.error("Failed to add member to project: {} for user: {}, error: {}", projectId, user.getUsername(), e.getMessage());
            return false;
        }
    }

    public boolean removeProjectMember(Long projectId, Long userId, Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = validateProjectUserAndMemberId(projectId, userId, authentication, model);
        if (userOpt.isEmpty()) {
            return false;
        }

        UserResponse user = userOpt.get();

        try {
            projectServiceClient.removeProjectMember(projectId, userId);
            addSuccessMessage(model, MessageConstants.MEMBER_REMOVED);
            log.info("Member removed from project: {} by user: {}", projectId, user.getUsername());
            return true;
        } catch (Exception e) {
            addErrorMessage(model, MessageConstants.FAILED_TO_REMOVE_MEMBER);
            log.error("Failed to remove member from project: {} for user: {}, error: {}", projectId, user.getUsername(), e.getMessage());
            return false;
        }
    }

    public Optional<UserResponse> getCurrentUser(Authentication authentication) {
        log.debug("Authentication: {}", authentication);

        if (!isValidAuthentication(authentication)) {
            log.warn("Authentication is not valid");
            return Optional.empty();
        }

        String username = authentication.getName();
        log.debug("Retrieving user info for username: {}", username);

        try {
            ApiResponse<UserResponse> apiResponse = userServiceClient.searchUsers("username", username);
            if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                UserResponse user = apiResponse.getData();
                log.debug("Successfully retrieved user: id={}, username={}", user.getId(), user.getUsername());
                return Optional.of(user);
            } else {
                log.error("User not found for username: {}", username);
                return Optional.empty();
            }
        } catch (Exception e) {
            log.error("Error retrieving user for username: {}, error: {}", username, e.getMessage());
            return Optional.empty();
        }
    }

    private boolean isValidAuthentication(Authentication authentication) {
        return authentication != null
                && authentication.isAuthenticated()
                && !isAnonymousUser(authentication.getName());
    }

    private boolean isAnonymousUser(String username) {
        return ModelAttributeConstants.ANONYMOUS_USER.equals(username);
    }

    private String extractErrorMessage(FeignException ex, String defaultMessage) {
        try {
            String responseBody = ex.contentUTF8();
            if (StringUtils.hasText(responseBody)) {
                if (responseBody.contains("\"message\"")) {
                    int messageStart = responseBody.indexOf(MESSAGE_JSON_KEY) + MESSAGE_JSON_KEY_LENGTH;
                    int messageEnd = responseBody.indexOf("\"", messageStart);
                    if (messageStart > MESSAGE_START_OFFSET && messageEnd > messageStart) {
                        String extractedMessage = responseBody.substring(messageStart, messageEnd);
                        if (isUserFriendlyMessage(extractedMessage)) {
                            return extractedMessage;
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.debug("Failed to extract error message from response: {}", e.getMessage());
        }
        return defaultMessage;
    }

    private boolean isUserFriendlyMessage(String message) {
        if (!StringUtils.hasText(message)) {
            return false;
        }

        String lowerMessage = message.toLowerCase();
        return !lowerMessage.contains("exception") &&
               !lowerMessage.contains("error") &&
               !lowerMessage.contains("null") &&
               !lowerMessage.contains("stack") &&
               message.length() < MAX_USER_FRIENDLY_MESSAGE_LENGTH;
    }

    private Optional<UserResponse> extractCurrentUserAndAddToModel(Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isEmpty()) {
            log.warn("Dashboard accessed without valid user");
            addErrorMessage(model, MessageConstants.USER_NOT_FOUND);
            return Optional.empty();
        }

        UserResponse user = userOpt.get();
        ModelUtils.addUser(model, user);
        return Optional.of(user);
    }

    private void addErrorMessage(Model model, String message) {
        ModelUtils.addError(model, message);
    }

    private void addSuccessMessage(Model model, String message) {
        ModelUtils.addSuccess(model, message);
    }

    private void handleServiceError(Model model, Exception e, String operation, String username) {
        if (e instanceof FeignException) {
            feignErrorHandler.handleFeignError((FeignException) e, model, operation, username);
        } else {
            feignErrorHandler.handleServiceError(e, model, operation, username);
        }
    }

    private boolean validateProjectId(Long projectId, Model model) {
        if (projectId == null || projectId <= MIN_VALID_ID) {
            addErrorMessage(model, MessageConstants.PROJECT_NOT_FOUND);
            log.warn("Invalid project ID provided: {}", projectId);
            return false;
        }
        return true;
    }

    private boolean validateUserId(Long userId, Model model) {
        if (userId == null || userId <= MIN_VALID_ID) {
            addErrorMessage(model, MessageConstants.USER_NOT_FOUND);
            log.warn("Invalid user ID provided: {}", userId);
            return false;
        }
        return true;
    }

    private boolean validateCreateProjectRequest(CreateProjectRequest request, Model model) {
        if (request == null) {
            addErrorMessage(model, MessageConstants.INVALID_PROJECT_DATA);
            log.warn("Create project request is null");
            return false;
        }

        if (!StringUtils.hasText(request.getName())) {
            addErrorMessage(model, "Project name is required");
            log.warn("Project name is empty or null");
            return false;
        }

        return true;
    }

    private void loadUserProjects(UserResponse user, Model model) {
        try {
            PagedResponse<ProjectResponse> projectsResponse = projectServiceClient.getUserProjects(user.getUsername());
            List<ProjectResponse> projects = projectsResponse.getContent();

            logProjectsResponse(projectsResponse, projects);

            if (projects != null && !projects.isEmpty()) {
                logProjectDetails(projects);
                ModelUtils.addProjects(model, projects);
            } else {
                ModelUtils.addProjects(model, Collections.emptyList());
            }
        } catch (Exception projectException) {
            log.warn("Failed to load projects for user: {}, error: {}", user.getUsername(), projectException.getMessage());
            ModelUtils.addProjects(model, Collections.emptyList());
        }
    }

    private void logProjectsResponse(PagedResponse<ProjectResponse> projectsResponse, List<ProjectResponse> projects) {
        log.info("Projects response details:");
        log.info("- Total elements: {}", projectsResponse.getTotalElements());
        log.info("- Total pages: {}", projectsResponse.getTotalPages());
        log.info("- Current page: {}", projectsResponse.getPage());
        log.info("- Page size: {}", projectsResponse.getSize());
        log.info("- Content size: {}", projects != null ? projects.size() : "null");
    }

    private void logProjectDetails(List<ProjectResponse> projects) {
        for (int i = 0; i < projects.size(); i++) {
            ProjectResponse project = projects.get(i);
            log.info("Project {}: id={}, name={}, ownerId={}, status={}",
                    i + LOG_INDEX_OFFSET, project.getId(), project.getName(), project.getOwnerId(), project.getStatus());
        }
    }

    private boolean executeProjectCreation(CreateProjectRequest request, UserResponse user, Model model) {
        try {
            log.info("Project name: {}, description: {}, user: {}",
                    request.getName(), request.getDescription(), user.getUsername());

            CreateProjectRequest projectRequest = CreateProjectRequest.builder()
                    .name(request.getName())
                    .description(request.getDescription())
                    .ownerId(user.getId())
                    .build();

            log.info("Calling ProjectService to create project...");
            return callProjectServiceForCreation(projectRequest, user, model);

        } catch (Exception e) {
            log.error("Error creating project: {} for user: {}, error: {}",
                    request.getName(), user.getUsername(), e.getMessage(), e);
            addErrorMessage(model, MessageConstants.FAILED_TO_CREATE_PROJECT + e.getMessage());
            return false;
        }
    }

    private boolean callProjectServiceForCreation(CreateProjectRequest projectRequest, UserResponse user, Model model) {
        try {
            ProjectResponse project = projectServiceClient.createProject(projectRequest);
            addSuccessMessage(model, String.format(MessageConstants.PROJECT_CREATED, project.getName()));
            log.info("=== PROJECT CREATED SUCCESSFULLY: {} ===", project.getName());
            return true;
        } catch (FeignException ex) {
            feignErrorHandler.handleFeignError(ex, model, "project creation", user.getUsername());
            return false;
        } catch (Exception createException) {
            feignErrorHandler.handleServiceError(createException, model, "project creation", user.getUsername());
            return false;
        }
    }

    public ServiceResult deleteProjectWithResult(Long projectId, Authentication authentication) {
        Model tempModel = ModelUtils.createTemporaryModel();
        boolean success = deleteProject(projectId, authentication, tempModel);

        if (success) {
            return ServiceResult.success("Project deleted successfully!");
        } else {
            String errorMessage = ModelUtils.hasError(tempModel)
                ? ModelUtils.getError(tempModel)
                : "Failed to delete project!";
            return ServiceResult.error(errorMessage);
        }
    }

    public ServiceResult removeProjectMemberWithResult(Long projectId, Long userId, Authentication authentication) {
        Model tempModel = ModelUtils.createTemporaryModel();
        boolean success = removeProjectMember(projectId, userId, authentication, tempModel);

        if (success) {
            return ServiceResult.success("Member removed successfully!");
        } else {
            String errorMessage = ModelUtils.hasError(tempModel)
                ? ModelUtils.getError(tempModel)
                : "Failed to remove member!";
            return ServiceResult.error(errorMessage);
        }
    }

    public void prepareDashboardWithProjects(Authentication authentication, Model model) {
        Optional<UserResponse> userOpt = getCurrentUser(authentication);
        if (userOpt.isPresent()) {
            loadUserProjects(userOpt.get(), model);
        } else {
            log.warn("Cannot load projects - user not found in authentication");
            ModelUtils.addProjects(model, Collections.emptyList());
        }
    }

}
